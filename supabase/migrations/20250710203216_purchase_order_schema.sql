create table "public"."purchase_order" (
	"purchase_order_id" uuid not null default gen_random_uuid(),
	"po_number" text not null,
	"description" text,
	"po_date" date not null,
	"project_id" uuid not null,
	"vendor_id" uuid not null,
	"account" text,
	"original_amount" numeric(15, 2),
	"co_amount" numeric(15, 2),
	"freight" numeric(15, 2),
	"tax" numeric(15, 2),
	"other" numeric(15, 2),
	"notes" text,
	"created_by_user_id" uuid not null default auth.uid (),
	"created_at" timestamp with time zone not null default timezone ('utc'::text, now()),
	"updated_at" timestamp with time zone not null default timezone ('utc'::text, now())
);

alter table "public"."purchase_order" enable row level security;

create table "public"."purchase_order_audit" (
	"audit_id" uuid not null default gen_random_uuid(),
	"operation_type" text not null,
	"changed_by" uuid not null,
	"changed_at" timestamp with time zone not null default now(),
	"old_values" jsonb,
	"new_values" jsonb,
	"purchase_order_id" uuid,
	"po_number" text,
	"description" text,
	"po_date" date,
	"project_id" uuid,
	"vendor_id" uuid,
	"account" text,
	"original_amount" numeric(15, 2),
	"co_amount" numeric(15, 2),
	"freight" numeric(15, 2),
	"tax" numeric(15, 2),
	"other" numeric(15, 2),
	"notes" text,
	"created_by_user_id" uuid,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);

alter table "public"."purchase_order_audit" enable row level security;

CREATE INDEX purchase_order_audit_changed_at_idx ON public.purchase_order_audit USING btree (changed_at);

CREATE INDEX purchase_order_audit_changed_by_idx ON public.purchase_order_audit USING btree (changed_by);

CREATE INDEX purchase_order_audit_operation_type_idx ON public.purchase_order_audit USING btree (operation_type);

CREATE UNIQUE INDEX purchase_order_audit_pkey ON public.purchase_order_audit USING btree (audit_id);

CREATE INDEX purchase_order_audit_project_id_idx ON public.purchase_order_audit USING btree (project_id);

CREATE INDEX purchase_order_audit_purchase_order_id_idx ON public.purchase_order_audit USING btree (purchase_order_id);

CREATE INDEX purchase_order_created_by_user_id_idx ON public.purchase_order USING btree (created_by_user_id);

CREATE UNIQUE INDEX purchase_order_pkey ON public.purchase_order USING btree (purchase_order_id);

CREATE INDEX purchase_order_po_date_idx ON public.purchase_order USING btree (po_date);

CREATE UNIQUE INDEX purchase_order_po_number_project_unique ON public.purchase_order USING btree (po_number, project_id);

CREATE INDEX purchase_order_project_id_idx ON public.purchase_order USING btree (project_id);

CREATE INDEX purchase_order_vendor_id_idx ON public.purchase_order USING btree (vendor_id);

alter table "public"."purchase_order"
add constraint "purchase_order_pkey" PRIMARY KEY using index "purchase_order_pkey";

alter table "public"."purchase_order_audit"
add constraint "purchase_order_audit_pkey" PRIMARY KEY using index "purchase_order_audit_pkey";

alter table "public"."purchase_order"
add constraint "purchase_order_created_by_user_id_fkey" FOREIGN KEY (created_by_user_id) REFERENCES profile (user_id) ON UPDATE RESTRICT ON DELETE RESTRICT not valid;

alter table "public"."purchase_order" validate constraint "purchase_order_created_by_user_id_fkey";

alter table "public"."purchase_order"
add constraint "purchase_order_po_number_project_unique" UNIQUE using index "purchase_order_po_number_project_unique";

alter table "public"."purchase_order"
add constraint "purchase_order_project_id_fkey" FOREIGN KEY (project_id) REFERENCES project (project_id) ON UPDATE RESTRICT ON DELETE RESTRICT not valid;

alter table "public"."purchase_order" validate constraint "purchase_order_project_id_fkey";

alter table "public"."purchase_order"
add constraint "purchase_order_vendor_id_fkey" FOREIGN KEY (vendor_id) REFERENCES vendor (vendor_id) ON UPDATE RESTRICT ON DELETE RESTRICT not valid;

alter table "public"."purchase_order" validate constraint "purchase_order_vendor_id_fkey";

alter table "public"."purchase_order_audit"
add constraint "purchase_order_audit_changed_by_fkey" FOREIGN KEY (changed_by) REFERENCES profile (user_id) ON UPDATE RESTRICT ON DELETE RESTRICT not valid;

alter table "public"."purchase_order_audit" validate constraint "purchase_order_audit_changed_by_fkey";

alter table "public"."purchase_order_audit"
add constraint "purchase_order_audit_operation_type_check" CHECK (
	(
		operation_type = ANY (
			ARRAY['INSERT'::text, 'UPDATE'::text, 'DELETE'::text]
		)
	)
) not valid;

alter table "public"."purchase_order_audit" validate constraint "purchase_order_audit_operation_type_check";

set
	check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.audit_purchase_order_changes () RETURNS trigger LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
DECLARE
    v_user_id UUID;
BEGIN
    -- Get the current user ID, fallback to a system user if not authenticated
    v_user_id := auth.uid();
    IF v_user_id IS NULL THEN
        -- Use a system user ID for operations not performed by authenticated users
        v_user_id := '********-0000-0000-0000-************'::UUID;
    END IF;

    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.purchase_order_audit (
            operation_type, changed_by, changed_at, old_values,
            purchase_order_id, po_number, description, po_date, project_id, vendor_id,
            account, original_amount, co_amount, freight, tax, other,
            notes, created_by_user_id, created_at, updated_at
        ) VALUES (
            'DELETE', v_user_id, NOW(), to_jsonb(OLD),
            OLD.purchase_order_id, OLD.po_number, OLD.description, OLD.po_date, OLD.project_id, OLD.vendor_id,
            OLD.account, OLD.original_amount, OLD.co_amount, OLD.freight, OLD.tax, OLD.other,
            OLD.notes, OLD.created_by_user_id, OLD.created_at, OLD.updated_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.purchase_order_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            purchase_order_id, po_number, description, po_date, project_id, vendor_id,
            account, original_amount, co_amount, freight, tax, other,
            notes, created_by_user_id, created_at, updated_at
        ) VALUES (
            'UPDATE', v_user_id, NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.purchase_order_id, NEW.po_number, NEW.description, NEW.po_date, NEW.project_id, NEW.vendor_id,
            NEW.account, NEW.original_amount, NEW.co_amount, NEW.freight, NEW.tax, NEW.other,
            NEW.notes, NEW.created_by_user_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.purchase_order_audit (
            operation_type, changed_by, changed_at, new_values,
            purchase_order_id, po_number, description, po_date, project_id, vendor_id,
            account, original_amount, co_amount, freight, tax, other,
            notes, created_by_user_id, created_at, updated_at
        ) VALUES (
            'INSERT', v_user_id, NOW(), to_jsonb(NEW),
            NEW.purchase_order_id, NEW.po_number, NEW.description, NEW.po_date, NEW.project_id, NEW.vendor_id,
            NEW.account, NEW.original_amount, NEW.co_amount, NEW.freight, NEW.tax, NEW.other,
            NEW.notes, NEW.created_by_user_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$function$;

CREATE OR REPLACE FUNCTION public.get_accessible_purchase_orders (project_id_param uuid) RETURNS TABLE (
	purchase_order_id uuid,
	po_number text,
	description text,
	po_date date,
	vendor_name text,
	vendor_id uuid,
	original_amount numeric,
	co_amount numeric,
	total_amount numeric,
	created_at timestamp with time zone,
	created_by_name text
) LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
DECLARE
	v_user_id UUID;
BEGIN
	-- Get the current user ID
	v_user_id := auth.uid();

	-- Check if user has access to the project
	IF NOT public.current_user_has_entity_access('project'::public.entity_type, project_id_param) THEN
		RETURN;
	END IF;

	-- Return purchase orders for the project with vendor and creator information
	RETURN QUERY
	SELECT
		po.purchase_order_id,
		po.po_number,
		po.description,
		po.po_date,
		v.name AS vendor_name,
		po.vendor_id,
		po.original_amount,
		po.co_amount,
		COALESCE(po.original_amount, 0) + COALESCE(po.co_amount, 0) + COALESCE(po.freight, 0) + COALESCE(po.tax, 0) + COALESCE(po.other, 0) AS total_amount,
		po.created_at,
		p.display_name AS created_by_name
	FROM public.purchase_order po
	LEFT JOIN public.vendor v ON po.vendor_id = v.vendor_id
	LEFT JOIN public.profile p ON po.created_by_user_id = p.user_id
	WHERE po.project_id = project_id_param
	ORDER BY po.po_date DESC, po.po_number;
END;
$function$;

grant delete on table "public"."purchase_order" to "anon";

grant insert on table "public"."purchase_order" to "anon";

grant references on table "public"."purchase_order" to "anon";

grant
select
	on table "public"."purchase_order" to "anon";

grant trigger on table "public"."purchase_order" to "anon";

grant
truncate on table "public"."purchase_order" to "anon";

grant
update on table "public"."purchase_order" to "anon";

grant delete on table "public"."purchase_order" to "authenticated";

grant insert on table "public"."purchase_order" to "authenticated";

grant references on table "public"."purchase_order" to "authenticated";

grant
select
	on table "public"."purchase_order" to "authenticated";

grant trigger on table "public"."purchase_order" to "authenticated";

grant
truncate on table "public"."purchase_order" to "authenticated";

grant
update on table "public"."purchase_order" to "authenticated";

grant delete on table "public"."purchase_order" to "service_role";

grant insert on table "public"."purchase_order" to "service_role";

grant references on table "public"."purchase_order" to "service_role";

grant
select
	on table "public"."purchase_order" to "service_role";

grant trigger on table "public"."purchase_order" to "service_role";

grant
truncate on table "public"."purchase_order" to "service_role";

grant
update on table "public"."purchase_order" to "service_role";

grant delete on table "public"."purchase_order_audit" to "anon";

grant insert on table "public"."purchase_order_audit" to "anon";

grant references on table "public"."purchase_order_audit" to "anon";

grant
select
	on table "public"."purchase_order_audit" to "anon";

grant trigger on table "public"."purchase_order_audit" to "anon";

grant
truncate on table "public"."purchase_order_audit" to "anon";

grant
update on table "public"."purchase_order_audit" to "anon";

grant delete on table "public"."purchase_order_audit" to "authenticated";

grant insert on table "public"."purchase_order_audit" to "authenticated";

grant references on table "public"."purchase_order_audit" to "authenticated";

grant
select
	on table "public"."purchase_order_audit" to "authenticated";

grant trigger on table "public"."purchase_order_audit" to "authenticated";

grant
truncate on table "public"."purchase_order_audit" to "authenticated";

grant
update on table "public"."purchase_order_audit" to "authenticated";

grant delete on table "public"."purchase_order_audit" to "service_role";

grant insert on table "public"."purchase_order_audit" to "service_role";

grant references on table "public"."purchase_order_audit" to "service_role";

grant
select
	on table "public"."purchase_order_audit" to "service_role";

grant trigger on table "public"."purchase_order_audit" to "service_role";

grant
truncate on table "public"."purchase_order_audit" to "service_role";

grant
update on table "public"."purchase_order_audit" to "service_role";

create policy "Users can create purchase orders for projects they can edit" on "public"."purchase_order" as permissive for insert to authenticated
with
	check (
		current_user_has_entity_role (
			'project'::entity_type,
			project_id,
			'editor'::membership_role
		)
	);

create policy "Users can delete purchase orders for projects they can admin" on "public"."purchase_order" as permissive for delete to authenticated using (
	current_user_has_entity_role (
		'project'::entity_type,
		project_id,
		'admin'::membership_role
	)
);

create policy "Users can update purchase orders for projects they can edit" on "public"."purchase_order" as permissive
for update
	to authenticated using (
		current_user_has_entity_role (
			'project'::entity_type,
			project_id,
			'editor'::membership_role
		)
	)
with
	check (
		current_user_has_entity_role (
			'project'::entity_type,
			project_id,
			'editor'::membership_role
		)
	);

create policy "Users can view purchase orders for accessible projects" on "public"."purchase_order" as permissive for
select
	to authenticated using (
		current_user_has_entity_access ('project'::entity_type, project_id)
	);

create policy "System can insert purchase order audit records" on "public"."purchase_order_audit" as permissive for insert to service_role
with
	check (true);

create policy "Users can view purchase order audit for accessible projects" on "public"."purchase_order_audit" as permissive for
select
	to authenticated using (
		current_user_has_entity_access ('project'::entity_type, project_id)
	);

CREATE TRIGGER audit_purchase_order_trigger
AFTER INSERT
OR DELETE
OR
UPDATE ON public.purchase_order FOR EACH ROW
EXECUTE FUNCTION audit_purchase_order_changes ();

CREATE TRIGGER update_updated_at BEFORE
UPDATE ON public.purchase_order FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column ();
