-- Purchase Order Audit Table Schema
-- Audit log of all changes to purchase order entries
-- Purchase Order Audit table
CREATE TABLE IF NOT EXISTS "public"."purchase_order_audit" (
	"audit_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"operation_type" "text" NOT NULL CHECK (
		"operation_type" IN ('INSERT', 'UPDATE', 'DELETE')
	),
	"changed_by" "uuid" NOT NULL,
	"changed_at" timestamp with time zone DEFAULT "now" () NOT NULL,
	"old_values" "jsonb",
	"new_values" "jsonb",
	-- Original table columns
	"purchase_order_id" "uuid",
	"po_number" "text",
	"description" "text",
	"po_date" "date",
	"project_id" "uuid",
	"vendor_id" "uuid",
	"account" "text",
	"original_amount" numeric(15, 2),
	"co_amount" numeric(15, 2),
	"freight" numeric(15, 2),
	"tax" numeric(15, 2),
	"other" numeric(15, 2),
	"notes" "text",
	"created_by_user_id" "uuid",
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);

ALTER TABLE "public"."purchase_order_audit" OWNER TO "postgres";

COMMENT ON TABLE "public"."purchase_order_audit" IS 'Audit log of all changes to purchase order entries';

-- Primary key constraint for audit table
ALTER TABLE ONLY "public"."purchase_order_audit"
ADD CONSTRAINT "purchase_order_audit_pkey" PRIMARY KEY ("audit_id");

-- Foreign key constraint for audit table
ALTER TABLE ONLY "public"."purchase_order_audit"
ADD CONSTRAINT "purchase_order_audit_changed_by_fkey" FOREIGN KEY ("changed_by") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

-- Indexes for audit table performance
CREATE INDEX "purchase_order_audit_purchase_order_id_idx" ON "public"."purchase_order_audit" USING "btree" ("purchase_order_id");

CREATE INDEX "purchase_order_audit_changed_by_idx" ON "public"."purchase_order_audit" USING "btree" ("changed_by");

CREATE INDEX "purchase_order_audit_changed_at_idx" ON "public"."purchase_order_audit" USING "btree" ("changed_at");

CREATE INDEX "purchase_order_audit_operation_type_idx" ON "public"."purchase_order_audit" USING "btree" ("operation_type");

CREATE INDEX "purchase_order_audit_project_id_idx" ON "public"."purchase_order_audit" USING "btree" ("project_id");

-- Enable Row Level Security for audit table
ALTER TABLE "public"."purchase_order_audit" ENABLE ROW LEVEL SECURITY;

-- Row Level Security Policies for Audit Table
CREATE POLICY "System can insert purchase order audit records" ON "public"."purchase_order_audit" FOR INSERT TO "service_role"
WITH
	CHECK (true);

CREATE POLICY "Users can view purchase order audit for accessible projects" ON "public"."purchase_order_audit" FOR
SELECT
	TO "authenticated" USING (
		"public"."current_user_has_entity_access" ('project'::"public"."entity_type", "project_id")
	);
