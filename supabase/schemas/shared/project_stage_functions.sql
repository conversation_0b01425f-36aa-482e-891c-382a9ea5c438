-- Project Stage Management Functions
-- This file contains functions for project stage operations
CREATE OR REPLACE FUNCTION "public"."complete_project_stage" (
	"p_project_stage_id" "uuid",
	"p_completion_notes" "text" DEFAULT NULL
) RETURNS UUID LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
DECLARE
	v_snapshot_id UUID;
	v_project_id UUID;
	v_is_ready BOOLEAN;
BEGIN
	-- Get project_id for permission check
	SELECT project_id INTO v_project_id
	FROM public.project_stage
	WHERE project_stage_id = p_project_stage_id;
	
	IF v_project_id IS NULL THEN
		RAISE EXCEPTION 'Project stage not found';
	END IF;
	
	-- Check if user can modify this project
	IF NOT public.can_modify_project(v_project_id) THEN
		RAISE EXCEPTION 'Insufficient permissions to complete this project stage';
	END IF;
	
	-- Check if stage is ready for completion
	SELECT public.is_stage_ready_for_completion(p_project_stage_id) INTO v_is_ready;
	
	IF NOT v_is_ready THEN
		RAISE EXCEPTION 'Project stage has incomplete checklist items and cannot be completed';
	END IF;
	
	-- Update the stage
	UPDATE public.project_stage
	SET
		date_completed = now(),
		completion_notes = p_completion_notes,
		updated_at = now()
	WHERE project_stage_id = p_project_stage_id;
	
	-- Create a budget snapshot
SELECT public.create_budget_snapshot(p_project_stage_id, p_completion_notes) INTO v_snapshot_id;
RETURN v_snapshot_id;
END;
$$;

ALTER FUNCTION "public"."complete_project_stage" (
	"p_project_stage_id" "uuid",
	"p_completion_notes" "text"
) OWNER TO "postgres";

COMMENT ON FUNCTION "public"."complete_project_stage" (
	"p_project_stage_id" "uuid",
	"p_completion_notes" "text"
) IS 'Completes a project stage if all checklist items are complete';

-- Function that depends on project table - moved here to resolve dependency order
CREATE OR REPLACE FUNCTION "public"."get_clients_with_permissions" ("org_name_param" "text") RETURNS TABLE (
	"client_id" "uuid",
	"name" "text",
	"description" "text",
	"logo_url" "text",
	"client_url" "text",
	"internal_url" "text",
	"internal_url_description" "text",
	"org_id" "uuid",
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"created_by_user_id" "uuid",
	"organization_name" "text",
	"project_count" bigint,
	"is_client_admin" boolean,
	"is_org_admin" boolean
) LANGUAGE "sql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
SELECT c.client_id,
	c.name,
	c.description,
	c.logo_url,
	c.client_url,
	c.internal_url,
	c.internal_url_description,
	c.org_id,
	c.created_at,
	c.updated_at,
	c.created_by_user_id,
	o.name AS organization_name,
	(
		SELECT COUNT(*)
		FROM public.project p
		WHERE p.client_id = c.client_id
	) AS project_count,
	public.current_user_has_entity_role('client', c.client_id, 'admin') AS is_client_admin,
	public.current_user_has_entity_role('organization', c.org_id, 'admin') AS is_org_admin
FROM public.client c
	INNER JOIN public.organization o ON o.org_id = c.org_id
WHERE o.name = org_name_param
	AND public.current_user_has_entity_access('client'::public.entity_type, c.client_id)
ORDER BY c.name;
$$;

ALTER FUNCTION "public"."get_clients_with_permissions" ("org_name_param" "text") OWNER TO "postgres";

COMMENT ON FUNCTION "public"."get_clients_with_permissions" ("org_name_param" "text") IS 'Gets clients with user permissions for an organization';
