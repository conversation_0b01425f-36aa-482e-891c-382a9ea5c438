/* eslint-disable @typescript-eslint/ban-ts-comment */
// @ts-nocheck
import { describe, it, expect, vi, beforeEach } from 'vitest';

// Mock SvelteKit modules first
vi.mock('@sveltejs/kit', async () => {
	const original = await vi.importActual('@sveltejs/kit');
	return {
		...original,
		fail: vi.fn().mockImplementation((code, data) => ({ status: code, ...data })),
	};
});

// Mock sveltekit-flash-message/server
vi.mock('sveltekit-flash-message/server', () => ({
	redirect: vi.fn().mockImplementation((url, message, cookies) => ({ url, message, cookies })),
}));

// Mock the dependencies
vi.mock('$lib/project_utils', () => ({
	getGatewayChecklistItems: vi.fn(),
	upsertGatewayChecklistItem: vi.fn(),
	isStageReadyForCompletion: vi.fn(),
	completeProjectStage: vi.fn(),
}));

// Mock server auth
vi.mock('$lib/server/auth', () => ({
	requireProject: vi.fn().mockReturnValue({
		org_name: 'test-org',
		client_name: 'test-client',
		project_name: 'test-project',
	}),
}));

// Mock superValidate
vi.mock('sveltekit-superforms/server', () => {
	return {
		superValidate: vi.fn().mockResolvedValue({
			valid: true,
			data: {
				items: [
					{ gateway_checklist_item_id: '1', status: 'Complete' },
					{ gateway_checklist_item_id: '2', status: 'Deferred' },
				],
			},
			errors: {},
		}),
		message: vi.fn((form, message) => ({ form, message })),
	};
});

// Mock superforms adapters
vi.mock('sveltekit-superforms/adapters', () => ({
	zod: vi.fn().mockImplementation((schema) => schema),
	zod4: vi.fn().mockImplementation((schema) => schema),
}));

// Import after mocks
import { actions } from '../../../routes/org/[org_name]/clients/[client_name]/projects/[project_name]/stage-[stage_order]/gateway/+page.server';

// Mock the supabase client
const mockSupabase = {
	from: vi.fn().mockImplementation(() => {
		return {
			select: vi.fn().mockReturnThis(),
			eq: vi.fn().mockReturnThis(),
			order: vi.fn().mockReturnThis(),
			limit: vi.fn().mockReturnThis(),
			maybeSingle: vi.fn().mockResolvedValue({
				data: { project_id: '123', client: { name: 'test-client' } },
				error: null,
			}),
			insert: vi.fn().mockResolvedValue({ error: null }),
		};
	}),
	rpc: vi.fn().mockResolvedValue({ data: true, error: null }),
};

// Mock the request
const mockRequest = {
	formData: vi.fn(),
};

// Mock the locals
const mockLocals = {
	supabase: mockSupabase,
};

// Mock the params
const mockParams = {
	client_name: 'test-client',
	project_name: 'test-project',
	org_name: 'test-org',
	stage_order: '1',
};

// Mock the cookies
const mockCookies = {};

describe('Gateway Server Actions', () => {
	beforeEach(async () => {
		vi.clearAllMocks();

		// Reset the upsertGatewayChecklistItem mock
		const { upsertGatewayChecklistItem } = await import('$lib/project_utils');
		vi.mocked(upsertGatewayChecklistItem).mockResolvedValue({
			data: null,
			statusUpdated: true,
			error: null,
		});
	});

	describe('updateChecklist action', () => {
		it('should validate and process checklist updates', async () => {
			// Create a valid form submission
			const formData = new FormData();
			formData.append('items[0].gateway_checklist_item_id', '1');
			formData.append('items[0].status', 'Complete');
			formData.append('items[1].gateway_checklist_item_id', '2');
			formData.append('items[1].status', 'Deferred');

			mockRequest.formData = vi.fn().mockResolvedValue(formData);

			// The superValidate function is already mocked at the top level

			// Call the action
			const result = await actions.updateChecklist({
				request: mockRequest,
				locals: mockLocals,
				params: mockParams,
				cookies: mockCookies,
			} as unknown as Parameters<typeof actions.updateChecklist>[0]);

			// Verify the result has the expected structure
			expect(result).toBeDefined();
			expect(result).toHaveProperty('form');
			expect(result.form.valid).toBe(true);
		});
	});

	// Additional tests for the updateScoring action would go here
});
