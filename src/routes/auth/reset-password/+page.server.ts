import { fail } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';
import { superValidate } from 'sveltekit-superforms/server';
import { zod4 as zod } from 'sveltekit-superforms/adapters';
import { resetPasswordSchema } from '$lib/schemas/auth';

export const load: PageServerLoad = async () => {
	const form = await superValidate(zod(resetPasswordSchema));
	return { form };
};

export const actions: Actions = {
	default: async ({ request, locals }) => {
		const form = await superValidate(request, zod(resetPasswordSchema));
		if (!form.valid) {
			return fail(400, { form });
		}

		const { error } = await locals.supabase.auth.resetPasswordForEmail(form.data.email);
		if (error) {
			form.errors.email = [error.message];
			return fail(400, { form });
		}

		return { success: true };
	},
};
