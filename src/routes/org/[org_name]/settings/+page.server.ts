import { fail, error } from '@sveltejs/kit';
import type { PageServerLoad, Actions } from './$types';
import { message, superValidate } from 'sveltekit-superforms/server';
import { zod4 as zod } from 'sveltekit-superforms/adapters';
import { organizationSchema } from '$lib/schemas/organization';

export const load: PageServerLoad = async ({ locals, params }) => {
	const { supabase } = locals;
	const { org_name } = params;

	// Get organization by name
	const { data: org, error: orgError } = await supabase
		.rpc('get_organization_by_name', { org_name_param: org_name })
		.maybeSingle();

	if (orgError || !org) throw error(404, 'Organization not found');

	const form = await superValidate(
		{ name: org.name, description: org.description ?? '', logo_url: org.logo_url ?? undefined },
		zod(organizationSchema),
	);
	return { form, organization: org };
};

export const actions: Actions = {
	default: async ({ request, locals, params }) => {
		const form = await superValidate(request, zod(organizationSchema));
		if (!form.valid) {
			return fail(400, { form });
		}

		const { supabase } = locals;
		const { org_name } = params;

		// Get organization by name
		const { data: org, error: orgError } = await supabase
			.rpc('get_organization_by_name', { org_name_param: org_name })
			.maybeSingle();

		if (orgError || !org) {
			form.errors.name = ['Organization not found'];
			return fail(404, { form });
		}

		const { error: updateError } = await supabase
			.from('organization')
			.update({
				name: form.data.name,
				description: form.data.description,
				logo_url: form.data.logo_url,
			})
			.eq('org_id', org.org_id)
			.select()
			.single();
		if (updateError) {
			form.errors.name = [updateError.message];
			return fail(500, { form });
		}

		return message(form, { type: 'success', text: 'Organization updated successfully.' });
	},
};
