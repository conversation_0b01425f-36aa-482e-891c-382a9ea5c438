<script lang="ts">
	import { goto } from '$app/navigation';
	import { superForm } from 'sveltekit-superforms/client';
	import { Button, buttonVariants } from '$lib/components/ui/button';
	import {
		Table,
		TableBody,
		TableCell,
		TableHead,
		TableHeader,
		TableRow,
	} from '$lib/components/ui/table';
	import {
		Card,
		CardContent,
		CardDescription,
		CardHeader,
		CardTitle,
	} from '$lib/components/ui/card';
	import { Badge } from '$lib/components/ui/badge';
	import { formatCurrency } from '$lib/utils';
	import { cn } from '$lib/utils';
	import { Calendar as CalendarIcon } from 'phosphor-svelte';
	import { Popover, PopoverContent, PopoverTrigger } from '$lib/components/ui/popover';
	import { Calendar } from '$lib/components/ui/calendar';
	import { format } from 'date-fns';
	import {
		DateFormatter,
		type DateValue,
		getLocalTimeZone,
		parseDate,
		today,
	} from '@internationalized/date';
	import * as Select from '$lib/components/ui/select';
	import * as Form from '$lib/components/ui/form';
	import { approvedChangeStatuses } from '$lib/schemas/risk';

	const { data } = $props();

	// Extract data from props
	const { project, client } = data;
	const approvedChanges = $derived(data.approvedChanges);
	const wbsItems = $derived(data.wbsItems);
	const budgetMap = $derived(data.budgetMap);

	// Setup filter form
	const filterForm = superForm(data.filterForm, {
		onUpdated({ form }) {
			// Build URL with filter parameters
			const params = new URLSearchParams();

			if (form.data.status && form.data.status !== 'all') {
				params.set('status', form.data.status);
			}

			if (form.data.date_from) {
				params.set('date_from', form.data.date_from);
			}

			if (form.data.date_to) {
				params.set('date_to', form.data.date_to);
			}

			if (form.data.wbs_library_item_id) {
				params.set('wbs_item', form.data.wbs_library_item_id.toString());
			}

			// Navigate to the filtered URL
			goto(`?${params.toString()}`);
		},
	});

	// Form bindings
	const { form: filterData, enhance: enhanceFilterForm } = filterForm;

	// Calculate total approved change amount
	const totalApprovedAmount = $derived(
		approvedChanges.reduce((sum, change) => sum + (change.potential_impact || 0), 0),
	);

	// Format date for display
	function formatDate(dateString: string | null) {
		if (!dateString) return '';
		return format(new Date(dateString), 'PPP');
	}

	// Get change owner display name
	function getChangeOwnerDisplay(change: (typeof approvedChanges)[0]) {
		if (change.risk_owner?.full_name) {
			return change.risk_owner.full_name;
		} else if (change.risk_owner?.email) {
			return change.risk_owner?.email;
		} else if (change.risk_owner_name) {
			return change.risk_owner_name;
		} else if (change.risk_owner_email) {
			return change.risk_owner_email;
		}
		return 'Not assigned';
	}

	// Get approved by display name
	function getApprovedByDisplay(change: (typeof approvedChanges)[0]) {
		if (change.approved_by?.full_name) {
			return change.approved_by.full_name;
		} else if (change.approved_by?.email) {
			return change.approved_by?.email;
		}
		return 'Unknown';
	}

	// Get original budget amount for WBS item
	function getOriginalBudgetAmount(wbsItemId: string | null) {
		if (!wbsItemId) return 0;
		return budgetMap[wbsItemId] || 0;
	}

	// Date formatter for displaying formatted dates
	const df = new DateFormatter('en-GB', {
		dateStyle: 'long',
	});

	// State variables for date pickers
	const startDate = $derived($filterData.date_from ? parseDate($filterData.date_from) : undefined);
	const endDate = $derived($filterData.date_to ? parseDate($filterData.date_to) : undefined);

	// Set placeholder for calendar
	let placeholder = $state<DateValue>(today(getLocalTimeZone()));
</script>

<svelte:head>
	<title>Approved Changes - {project.name} - {client.name}</title>
</svelte:head>

<div class="mx-auto mb-12 px-2 py-6 sm:px-6 lg:px-12">
	<div class="mb-6 flex items-center justify-between">
		<div>
			<h1 class="text-3xl font-bold tracking-tight">Approved Changes</h1>
			<p class="text-muted-foreground mt-2">
				Changes that have been approved and are being implemented
			</p>
		</div>
	</div>

	<!-- Filter Card -->
	<Card class="mb-6">
		<CardHeader>
			<CardTitle>Filters</CardTitle>
			<CardDescription>Filter approved changes by status, date, or WBS item</CardDescription>
		</CardHeader>
		<CardContent>
			<form
				action="?/applyFilters"
				method="POST"
				use:enhanceFilterForm
				class="grid grid-cols-1 gap-4 md:grid-cols-4"
			>
				<div>
					<Form.Field form={filterForm} name="status">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>Status</Form.Label>
								<Select.Root type="single" bind:value={$filterData.status} name={props.name}>
									<Select.Trigger {...props}>
										{$filterData.status
											? $filterData.status === 'all'
												? 'All Statuses'
												: $filterData.status.charAt(0).toUpperCase() + $filterData.status.slice(1)
											: 'Select status'}
									</Select.Trigger>
									<Select.Content>
										<Select.Item value="all" label="All Statuses" />
										{#each approvedChangeStatuses as status (status)}
											<Select.Item
												value={status}
												label={status.charAt(0).toUpperCase() + status.slice(1)}
											/>
										{/each}
									</Select.Content>
								</Select.Root>
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>
				</div>

				<div>
					<Form.Field form={filterForm} name="date_from">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>From Date</Form.Label>
								<Popover>
									<PopoverTrigger
										{...props}
										class={cn(
											buttonVariants({ variant: 'outline' }),
											'w-full justify-start text-left font-normal',
											!startDate && 'text-muted-foreground',
										)}
									>
										{startDate
											? df.format(startDate.toDate(getLocalTimeZone()))
											: 'Pick a start date'}
										<CalendarIcon class="ml-auto h-4 w-4 opacity-50" />
									</PopoverTrigger>
									<PopoverContent class="w-auto p-0" side="top">
										<Calendar
											type="single"
											value={startDate as DateValue}
											bind:placeholder
											calendarLabel="Start date"
											onValueChange={(v) => {
												if (v) {
													$filterData.date_from = v.toString();
												} else {
													$filterData.date_from = undefined;
												}
											}}
										/>
									</PopoverContent>
								</Popover>
								<Form.FieldErrors />
								<input hidden value={$filterData.date_from} name={props.name} />
							{/snippet}
						</Form.Control>
					</Form.Field>
				</div>

				<div>
					<Form.Field form={filterForm} name="date_to">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>To Date</Form.Label>
								<Popover>
									<PopoverTrigger
										{...props}
										class={cn(
											buttonVariants({ variant: 'outline' }),
											'w-full justify-start text-left font-normal',
											!endDate && 'text-muted-foreground',
										)}
									>
										{endDate ? df.format(endDate.toDate(getLocalTimeZone())) : 'Pick an end date'}
										<CalendarIcon class="ml-auto h-4 w-4 opacity-50" />
									</PopoverTrigger>
									<PopoverContent class="w-auto p-0" side="top">
										<Calendar
											type="single"
											value={endDate as DateValue}
											bind:placeholder
											calendarLabel="End date"
											onValueChange={(v) => {
												if (v) {
													$filterData.date_to = v.toString();
												} else {
													$filterData.date_to = undefined;
												}
											}}
										/>
									</PopoverContent>
								</Popover>
								<Form.FieldErrors />
								<input hidden value={$filterData.date_to} name={props.name} />
							{/snippet}
						</Form.Control>
					</Form.Field>
				</div>

				<div>
					<Form.Field form={filterForm} name="wbs_library_item_id">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>WBS Item</Form.Label>
								<Select.Root
									type="single"
									bind:value={$filterData.wbs_library_item_id}
									name={props.name}
								>
									<Select.Trigger {...props}>
										{$filterData.wbs_library_item_id
											? wbsItems.find(
													(item) => item.wbs_library_item_id === $filterData.wbs_library_item_id,
												)
												? `${wbsItems.find((item) => item.wbs_library_item_id === $filterData.wbs_library_item_id)?.code} - ${wbsItems.find((item) => item.wbs_library_item_id === $filterData.wbs_library_item_id)?.description}`
												: 'Select WBS item'
											: 'All WBS Items'}
									</Select.Trigger>
									<Select.Content>
										<Select.Item value="" label="All WBS Items" />
										{#each wbsItems as item (item.wbs_library_item_id)}
											<Select.Item
												value={item.wbs_library_item_id.toString()}
												label={`${item.code} - ${item.description}`}
											/>
										{/each}
									</Select.Content>
								</Select.Root>
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>
				</div>

				<div class="flex justify-end md:col-span-4">
					<Button type="submit">Apply Filters</Button>
				</div>
			</form>
		</CardContent>
	</Card>

	<!-- Summary Card -->
	<Card class="mb-6">
		<CardHeader>
			<CardTitle>Approved Changes Summary</CardTitle>
		</CardHeader>
		<CardContent>
			<div class="grid grid-cols-1 gap-4 md:grid-cols-2">
				<div>
					<h3 class="text-lg font-medium">Total Approved Changes</h3>
					<p class="text-3xl font-bold">{approvedChanges.length}</p>
				</div>
				<div>
					<h3 class="text-lg font-medium">Total Approved Amount</h3>
					<p class="text-3xl font-bold">{formatCurrency(totalApprovedAmount)}</p>
				</div>
			</div>
		</CardContent>
	</Card>

	<!-- Approved Changes Table -->
	<div class="rounded-md border">
		<Table class="w-full table-fixed">
			<TableHeader>
				<TableRow>
					<TableHead class="min-w-80">Title</TableHead>
					<TableHead class="min-w-80">WBS Item</TableHead>
					<TableHead class="w-40 text-right">Original Budget</TableHead>
					<TableHead class="w-40 text-right">Change Amount</TableHead>
					<TableHead class="w-40 text-right">Date Approved</TableHead>
					<TableHead class="w-52">Change Owner</TableHead>
					<TableHead class="w-52">Approved By</TableHead>
				</TableRow>
			</TableHeader>
			<TableBody>
				{#if approvedChanges.length === 0}
					<TableRow>
						<TableCell class="py-4 text-center" colspan={7}>No approved changes found</TableCell>
					</TableRow>
				{:else}
					{#each approvedChanges as change (change.approved_change_id)}
						<TableRow>
							<TableCell class="font-medium">
								<div>
									<p class="max-w-80 text-wrap">{change.title}</p>
									<p
										class="text-muted-foreground max-w-80 truncate text-xs"
										title={change.description}
									>
										{change.description}
									</p>
								</div>
							</TableCell>
							<TableCell class="w-80">
								<p class="max-w-80 text-wrap">
									{change.wbs_item
										? `${change.wbs_item.code} - ${change.wbs_item.description}`
										: '-'}
								</p>
							</TableCell>
							<TableCell class="text-right">
								{change.wbs_library_item_id
									? formatCurrency(getOriginalBudgetAmount(change.wbs_library_item_id))
									: '-'}
							</TableCell>
							<TableCell class="text-right">
								<Badge variant="secondary" class="font-bold">
									{change.potential_impact ? formatCurrency(change.potential_impact) : '-'}
								</Badge>
							</TableCell>
							<TableCell class="text-right">{formatDate(change.date_approved)}</TableCell>
							<TableCell>{getChangeOwnerDisplay(change)}</TableCell>
							<TableCell>{getApprovedByDisplay(change)}</TableCell>
						</TableRow>
					{/each}
				{/if}
			</TableBody>
		</Table>
	</div>
</div>
