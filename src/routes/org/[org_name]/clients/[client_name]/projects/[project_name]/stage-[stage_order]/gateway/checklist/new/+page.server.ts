import { superValidate } from 'sveltekit-superforms/server';
import { zod4 as zod } from 'sveltekit-superforms/adapters';
import { type Actions, fail } from '@sveltejs/kit';
import { redirect } from 'sveltekit-flash-message/server';
import { gatewayChecklistItemSchema } from '$lib/schemas/gateway-checklist';
import { requireProject } from '$lib/server/auth';
import { upsertGatewayChecklistItem } from '$lib/project_utils';

export async function load({ locals, params, cookies }) {
	const { supabase } = locals;
	const { org_name, client_name, project_name, stage_order } = params;

	requireProject(params, cookies);

	// Get the project
	const { data: project, error: projectError } = await supabase
		.from('project')
		.select('*, client!inner(name), project_stage(*)')
		.eq('client.name', client_name)
		.eq('name', project_name)
		.eq('project_stage.stage_order', Number(stage_order))
		.limit(1)
		.maybeSingle();

	if (projectError || !project) {
		console.error('Error fetching project:', projectError);
		return redirect(
			`/org/${org_name}/clients/${client_name}`,
			{ type: 'error', message: 'Project not found' },
			cookies,
		);
	}

	// Find the current active stage (first incomplete stage)
	const currentStage =
		project.project_stage.find((stage) => !stage.date_completed) || project.project_stage?.[0];

	if (!currentStage) {
		return redirect(
			`/org/${org_name}/clients/${client_name}/projects/${project_name}`,
			{ type: 'error', message: 'No active project stage found' },
			cookies,
		);
	}

	// Check if user can edit the project
	const { data: canEditProject, error: canEditProjectError } = await supabase.rpc(
		'can_modify_project',
		{
			project_id_param: project.project_id,
		},
	);

	if (canEditProjectError) {
		console.error('Error checking project edit permissions:', canEditProjectError);
	}

	// Create a form with the gateway checklist item schema
	const form = await superValidate(zod(gatewayChecklistItemSchema));

	return {
		project,
		currentStage,
		canEditProject,
		form,
	};
}

export const actions: Actions = {
	default: async ({ request, locals, params, cookies }) => {
		const { supabase } = locals;
		const { org_name, client_name, project_name } = requireProject(params, cookies);

		// Get form data
		const form = await superValidate(request, zod(gatewayChecklistItemSchema));
		if (!form.valid) {
			return fail(400, { form });
		}

		const { data: project } = await supabase
			.from('project')
			.select('*, client!inner(name)')
			.eq('client.name', client_name)
			.eq('name', project_name)
			.limit(1)
			.maybeSingle();

		if (!project) {
			return fail(404, { form, message: { type: 'error', text: 'Project not found' } });
		}

		// Check permissions
		const { data: canEdit } = await supabase.rpc('can_modify_project', {
			project_id_param: project.project_id,
		});

		if (!canEdit) {
			return fail(403, {
				form,
				message: { type: 'error', text: 'You do not have permission to edit this project' },
			});
		}

		// Create the new checklist item with initial 'Incomplete' status
		const { data: upsertData, error: upsertError } = await upsertGatewayChecklistItem(
			supabase,
			{
				project_stage_id: form.data.project_stage_id,
				name: form.data.name,
				description: form.data.description,
			},
			'Incomplete', // Set initial status explicitly
		);

		console.log({ upsertData });
		if (upsertError) {
			console.error('Error creating checklist item:', upsertError);
			return fail(500, {
				form,
				message: { type: 'error', text: 'Failed to create checklist item' },
			});
		}

		// Redirect back to the checklist page with a success message
		return redirect(
			`/org/${org_name}/clients/${client_name}/projects/${project_name}/stage-${params.stage_order}/gateway/checklist`,
			{ type: 'success', message: 'Checklist item created successfully' },
			cookies,
		);
	},
};
