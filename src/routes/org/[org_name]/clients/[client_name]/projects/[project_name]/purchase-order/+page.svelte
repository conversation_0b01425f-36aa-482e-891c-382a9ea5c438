<script lang="ts">
	import type { PageData } from './$types';
	import { Button } from '$lib/components/ui/button';
	import { Plus as PlusIcon, DotsThreeVertical as DotsThreeVerticalIcon } from 'phosphor-svelte';
	import * as DropdownMenu from '$lib/components/ui/dropdown-menu';
	import * as Dialog from '$lib/components/ui/dialog';
	import {
		Table,
		TableBody,
		TableCell,
		TableHead,
		TableHeader,
		TableRow,
	} from '$lib/components/ui/table';
	import { formatCurrency } from '$lib/schemas/purchase_order';
	import { page } from '$app/state';
	import { goto } from '$app/navigation';
	import { toast } from 'svelte-sonner';

	const { data }: { data: PageData } = $props();
	const { purchaseOrders, project } = data;

	let deleteDialogOpen = $state(false);
	let purchaseOrderToDelete = $state<(typeof purchaseOrders)[0] | null>(null);
	let isDeleting = $state(false);

	function formatDate(dateString: string): string {
		return new Date(dateString).toLocaleDateString('en-GB', {
			year: 'numeric',
			month: 'short',
			day: 'numeric',
		});
	}

	function openDeleteDialog(purchaseOrder: (typeof purchaseOrders)[0]) {
		purchaseOrderToDelete = purchaseOrder;
		deleteDialogOpen = true;
	}

	async function deletePurchaseOrder() {
		if (!purchaseOrderToDelete) return;

		isDeleting = true;
		try {
			const response = await fetch(
				`/org/${encodeURIComponent(page.params.org_name)}/clients/${encodeURIComponent(page.params.client_name)}/projects/${encodeURIComponent(page.params.project_name)}/purchase-order/${purchaseOrderToDelete.purchase_order_id}`,
				{
					method: 'DELETE',
				},
			);

			if (response.ok) {
				toast.success(`Purchase order ${purchaseOrderToDelete.po_number} deleted successfully`);
				// Refresh the page to update the list
				goto(page.url, { invalidateAll: true });
			} else {
				toast.error('Failed to delete purchase order');
			}
		} catch (error) {
			console.error('Error deleting purchase order:', error);
			toast.error('Failed to delete purchase order');
		} finally {
			isDeleting = false;
			deleteDialogOpen = false;
			purchaseOrderToDelete = null;
		}
	}
</script>

<div class="container mx-auto py-8">
	<div class="mb-6 flex items-center justify-between">
		<div>
			<h1 class="text-2xl font-semibold">Purchase Orders</h1>
			<p class="text-muted-foreground mt-1">
				Manage purchase orders for {project.name}
			</p>
		</div>
		<Button
			href="/org/{encodeURIComponent(page.params.org_name)}/clients/{encodeURIComponent(
				page.params.client_name,
			)}/projects/{encodeURIComponent(page.params.project_name)}/purchase-order/new"
			class="gap-2"
		>
			<PlusIcon class="size-4" />
			New Purchase Order
		</Button>
	</div>

	{#if purchaseOrders.length === 0}
		<div class="rounded-lg border border-dashed p-8 text-center">
			<p class="text-muted-foreground">
				No purchase orders found. Create your first purchase order to get started.
			</p>
		</div>
	{:else}
		<div class="rounded-md border">
			<Table>
				<TableHeader>
					<TableRow>
						<TableHead class="w-32">PO Number</TableHead>
						<TableHead class="w-48">Vendor</TableHead>
						<TableHead class="w-64">Description</TableHead>
						<TableHead class="w-32 text-right">Total Amount</TableHead>
						<TableHead class="w-32">PO Date</TableHead>
						<TableHead class="w-32">Created</TableHead>
						<TableHead class="w-20 pr-4 text-right">Actions</TableHead>
					</TableRow>
				</TableHeader>
				<TableBody>
					{#each purchaseOrders as purchaseOrder (purchaseOrder.purchase_order_id)}
						<TableRow>
							<TableCell class="font-medium">
								{purchaseOrder.po_number}
							</TableCell>
							<TableCell>
								{purchaseOrder.vendor_name || 'Unknown Vendor'}
							</TableCell>
							<TableCell>
								<div class="max-w-64 truncate">
									{purchaseOrder.description || 'No description'}
								</div>
							</TableCell>
							<TableCell class="text-right font-medium">
								{formatCurrency(purchaseOrder.total_amount)}
							</TableCell>
							<TableCell>
								{formatDate(purchaseOrder.po_date)}
							</TableCell>
							<TableCell>
								{formatDate(purchaseOrder.created_at)}
							</TableCell>
							<TableCell class="text-right">
								<DropdownMenu.Root>
									<DropdownMenu.Trigger>
										{#snippet child({ props })}
											<Button {...props} variant="ghost" class="size-8 p-0">
												<span class="sr-only">Open menu</span>
												<DotsThreeVerticalIcon class="size-4" />
											</Button>
										{/snippet}
									</DropdownMenu.Trigger>
									<DropdownMenu.Content align="end">
										<DropdownMenu.Item>
											<a
												href="/org/{encodeURIComponent(
													page.params.org_name,
												)}/clients/{encodeURIComponent(
													page.params.client_name,
												)}/projects/{encodeURIComponent(
													page.params.project_name,
												)}/purchase-order/{purchaseOrder.purchase_order_id}/edit"
												class="flex w-full"
											>
												Edit
											</a>
										</DropdownMenu.Item>
										<DropdownMenu.Item onclick={() => openDeleteDialog(purchaseOrder)}>
											Delete
										</DropdownMenu.Item>
									</DropdownMenu.Content>
								</DropdownMenu.Root>
							</TableCell>
						</TableRow>
					{/each}
				</TableBody>
			</Table>
		</div>
	{/if}
</div>

<!-- Delete Confirmation Dialog -->
<Dialog.Root bind:open={deleteDialogOpen}>
	<Dialog.Content>
		<Dialog.Header>
			<Dialog.Title>Delete Purchase Order</Dialog.Title>
			<Dialog.Description>
				Are you sure you want to delete purchase order "{purchaseOrderToDelete?.po_number}"? This
				action cannot be undone.
			</Dialog.Description>
		</Dialog.Header>
		<Dialog.Footer>
			<Button variant="outline" onclick={() => (deleteDialogOpen = false)} disabled={isDeleting}>
				Cancel
			</Button>
			<Button variant="destructive" onclick={deletePurchaseOrder} disabled={isDeleting}>
				{isDeleting ? 'Deleting...' : 'Delete'}
			</Button>
		</Dialog.Footer>
	</Dialog.Content>
</Dialog.Root>
