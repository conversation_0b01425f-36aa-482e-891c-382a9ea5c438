/* eslint-disable no-irregular-whitespace */
import { clsx, type ClassValue } from 'clsx';
import { format } from 'date-fns';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
	return twMerge(clsx(inputs));
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export type WithoutChild<T> = T extends { child?: any } ? Omit<T, 'child'> : T;
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export type WithoutChildren<T> = T extends { children?: any } ? Omit<T, 'children'> : T;
export type WithoutChildrenOrChild<T> = WithoutChildren<WithoutChild<T>>;
export type WithElementRef<T, U extends HTMLElement = HTMLElement> = T & { ref?: U | null };

export const SUPABASE_SYMBOL = Symbol('SUPABASE');

/**
 * Format a date string into a more readable format
 * @param dateString ISO date string
 * @returns Formatted date string (YYYY-MM-DD)
 */
export function formatDate(dateString: string, style = 'MMM d, yyyy') {
	if (!dateString) return '';
	return format(new Date(dateString), style);
}

/**
 * Format a number into a currency string in Swedish Krona (SEK) format
 * @param value The value to format
 * @description Formats a number into a currency string in Swedish Krona (SEK) format
 * @example formatCurrency(1234.56) // "1 234,56 kr"
 * @returns {string} The formatted currency string
 */
export function formatCurrency(value: number | null | undefined) {
	if (value === null || value === undefined) return '';
	return new Intl.NumberFormat('en-GB', {
		// style: 'currency',
		// currency: 'SEK',
		minimumFractionDigits: 0,
		maximumFractionDigits: 0,
	}).format(value);
}

// Format percentage
export function formatPercentage(value: number | null | undefined): string {
	if (value === null || value === undefined) return '';
	return `+/- ${value}%`;
}

export function capitalizeFirstLetter(s: string) {
	return String(s).charAt(0).toUpperCase() + String(s).slice(1);
}
